import { MagnifyingGlassIcon } from "@radix-ui/react-icons";
import { Button, TextField } from "@radix-ui/themes";

interface SearchFiltersProps {
  searchQuery: string;
  onSearchChange: (value: string) => void;
  onExportData: () => void;
}

export const SearchFilters = ({
  searchQuery,
  onSearchChange,
  onExportData,
}: SearchFiltersProps) => {
  return (
    <div className="flex flex-col md:flex-row gap-4 justify-between items-start md:items-center bg-gray-50 p-4 rounded-lg border">
      <div className="flex-1 max-w-md">
        <TextField.Root
          placeholder="Search for portfolio ID or email address"
          value={searchQuery}
          onChange={(e) => onSearchChange(e.target.value)}
          size="3"
        >
          <TextField.Slot side="left">
            <MagnifyingGlassIcon height="16" width="16" />
          </TextField.Slot>
          <TextField.Slot side="right">
            <Button
              size="1"
              variant="soft"
              color="gray"
              onClick={() => onSearchChange("")}
              style={{ background: "none" }}
            >
              Search
            </Button>
          </TextField.Slot>
        </TextField.Root>
      </div>

      <Button
        size="3"
        variant="soft"
        color="gray"
        onClick={onExportData}
        style={{ background: "none" }}
      >
        Export data
      </Button>
    </div>
  );
};
