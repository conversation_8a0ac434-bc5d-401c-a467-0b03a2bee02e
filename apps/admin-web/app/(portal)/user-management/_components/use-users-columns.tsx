import { ColumnDef, createColumnHelper } from "@tanstack/react-table";
import { UserSummaryResponse } from "@/api/data-contracts";
import { Text } from "@radix-ui/themes";

const columnHelper = createColumnHelper<UserSummaryResponse>();

export const useUsersColumns = () => {
  const columns: ColumnDef<UserSummaryResponse, any>[] = [
    columnHelper.accessor("publicId", {
      header: "Portfolio ID",
      cell: (info) => {
        const value = info.getValue();
        return (
          <Text size="2" color="orange" weight="medium">
            {value || "-"}
          </Text>
        );
      },
      size: 150,
    }),
    columnHelper.accessor("email", {
      header: "Email",
      cell: (info) => {
        const value = info.getValue();
        return (
          <Text size="2">
            {value || "-"}
          </Text>
        );
      },
      size: 250,
    }),
    columnHelper.accessor("createdAt", {
      header: "Account creation time",
      cell: (info) => {
        const value = info.getValue();
        if (!value) return <Text size="2">-</Text>;
        
        const date = new Date(value);
        const formattedDate = date.toLocaleDateString("en-GB", {
          day: "2-digit",
          month: "2-digit",
          year: "2-digit",
        });
        const formattedTime = date.toLocaleTimeString("en-GB", {
          hour: "2-digit",
          minute: "2-digit",
          second: "2-digit",
          hour12: false,
        });
        
        return (
          <Text size="2">
            {formattedDate} {formattedTime}
          </Text>
        );
      },
      size: 180,
    }),
    columnHelper.accessor("totalInvestment", {
      header: "Total invested amount (USD)",
      cell: (info) => {
        const value = info.getValue();
        if (!value?.amount) {
          return <Text size="2">-</Text>;
        }
        
        const amount = parseFloat(value.amount);
        const formattedAmount = amount.toLocaleString("en-US", {
          minimumFractionDigits: 0,
          maximumFractionDigits: 0,
        });
        
        return (
          <Text size="2" color="green" weight="medium">
            +{formattedAmount}
          </Text>
        );
      },
      size: 200,
    }),
    columnHelper.accessor("countryOfResidence", {
      header: "Country of residence",
      cell: (info) => {
        const value = info.getValue();
        return (
          <Text size="2">
            {value || "-"}
          </Text>
        );
      },
      size: 180,
    }),
  ];

  return columns;
};
