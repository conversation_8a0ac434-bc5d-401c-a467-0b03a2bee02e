import { Container } from "@repo/ui/layout/container";
import { Heading, Text } from "@radix-ui/themes";
import classNames from "classnames";
import Image from "next/image";

export const InfoLayout = ({
  icon,
  iconAlt,
  title,
  description,
  children,
  className,
  smLeftAlign,
}: {
  icon: string;
  iconAlt: string;
  title: string;
  description: string;
  children?: React.ReactNode;
  className?: string;
  smLeftAlign?: boolean;
}) => {
  return (
    <div className={className}>
      <div
        className={classNames(
          "flex flex-col gap-6 items-center justify-center py-10",
        )}
      >
        <Image
          unoptimized
          className={smLeftAlign ? "md:mx-auto" : "mx-auto"}
          src={icon}
          alt={iconAlt}
          width={120}
          height={120}
        />
        <div
          className={classNames(
            "flex flex-col",
            smLeftAlign ? "md:text-center" : "text-center",
          )}
        >
          <div className="flex flex-col gap-4">
            <Heading as="h2" size="7" weight="medium" className="mb-4">
              {title}
            </Heading>

            <Text size="2" color="gray">
              {description}
            </Text>
          </div>
        </div>

        {children}
      </div>
    </div>
  );
};
