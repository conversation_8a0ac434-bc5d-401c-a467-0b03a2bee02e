"use client";
import { PrimaryButton, SecondaryButton } from "@repo/ui/form-button";
import { InfoLayout } from "@/ui-components/info-layout";
import { StatusItem, StatusItemProps } from "./status-item";
import { useTranslations } from "next-intl";
import { goToLogout } from "@/utils/logout";
import { useKycStatus } from "@/hooks/use-kyc-status";

export const AccountStatus = ({
  onStartVerification,
}: {
  onStartVerification: () => void;
}) => {
  const t = useTranslations("Portal.KYC.accountStatus");
  const { kycStatus } = useKycStatus();

  const status: StatusItemProps[] = [
    {
      icon: "/graphics/orange/create-account.png",
      title: t("step1"),
      status: "complete",
    },
    {
      icon: "/graphics/orange/verify.png",
      title: t("step2"),
      status: "pending",
    },
    {
      icon: "/graphics/orange/link-card.png",
      title: t("step3"),
      status: null,
    },
  ];

  return (
    <InfoLayout
      className="py-6 md:py-10"
      icon="/graphics/orange/verify.png"
      iconAlt="verify"
      title={t("completeAccountSetup")}
      description={t("setupDescription")}
    >
      <div>
        {status.map((item, index) => (
          <StatusItem key={index} {...item} />
        ))}
      </div>

      <div className="flex flex-col gap-4">
        <PrimaryButton
          onClick={onStartVerification}
          disabled={kycStatus === "PENDING"}
        >
          {t("verifyIdentity")}
        </PrimaryButton>
        <SecondaryButton onClick={goToLogout}>{t("signOut")}</SecondaryButton>
      </div>
    </InfoLayout>
  );
};
