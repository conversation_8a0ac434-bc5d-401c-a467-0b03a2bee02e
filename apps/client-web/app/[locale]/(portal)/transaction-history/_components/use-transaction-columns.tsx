import { useTranslations } from "next-intl";
import { IconButton } from "@radix-ui/themes";
import { DotsHorizontalIcon } from "@radix-ui/react-icons";
import { ColumnDef, createColumnHelper } from "@tanstack/react-table";
import { AppMenu } from "@repo/ui/app-menu";
import { CopyButton } from "@/ui-components/copy-button";
import { TransactionDTO } from "@/api/data-contracts";
import { formatDate } from "../utils";
import {
  AmountDisplay,
  TransactionStatus,
  TransactionType,
} from "./transaction-status";

const DetailsButton = ({ onViewDetails }: { onViewDetails: () => void }) => {
  return (
    <AppMenu
      config={[
        {
          key: "view-details",
          label: <span>View details</span>,
          onClick: onViewDetails,
        },
      ]}
    >
      <IconButton color="gray" highContrast variant="ghost">
        <DotsHorizontalIcon />
      </IconButton>
    </AppMenu>
  );
};

export const useTransactionColumns = ({
  onViewDetails,
  disableActions = false,
}: {
  onViewDetails: (id: string) => void;
  disableActions?: boolean;
}) => {
  const t = useTranslations("Portal.TransactionHistory");
  const columnHelper = createColumnHelper<TransactionDTO>();

  const columns: ColumnDef<TransactionDTO, any>[] = [
    columnHelper.accessor("id", {
      minSize: 320,
      size: 320,
      header: t("transactionId"),
      cell: (info) => (
        <div className="flex items-center gap-2">
          <span>{info.getValue()}</span>
          <CopyButton text={info.getValue() || ""} />
        </div>
      ),
    }),
    columnHelper.accessor("transactionType", {
      header: t("type"),
      cell: (info) => {
        const type = info.getValue();
        return <TransactionType type={type} />;
      },
    }),
    columnHelper.accessor("amount", {
      size: 200,
      header: t("totalAmount"),
      cell: (info) => {
        const amount = info.getValue();
        return <AmountDisplay amount={amount} />;
      },
    }),
    columnHelper.accessor("currency", {
      header: t("currency"),
    }),

    columnHelper.accessor("createdAt", {
      size: 200,
      header: t("createdDate"),
      cell: (info) => {
        const value = info.getValue();
        return <span className="text-nowrap">{formatDate(value)}</span>;
      },
    }),
    columnHelper.accessor("status", {
      header: t("status"),
      cell: (info) => {
        return <TransactionStatus status={info.getValue()} />;
      },
    }),
  ];

  if (!disableActions) {
    columns.push(
      columnHelper.display({
        id: "action",
        header: t("action"),
        size: 80,
        cell: (info) => {
          return (
            <div className="flex justify-center">
              <DetailsButton
                onViewDetails={() => {
                  const id = info.row.getValue("id") as string;
                  console.log(id);
                  onViewDetails(id);
                }}
              />
            </div>
          );
        },
      }),
    );
  }

  return columns;
};
