import service from "@/api";
import { useQuery } from "@tanstack/react-query";

export const useTransactionHistoryData = ({
  currentPage,
  pageSize,
  status,
}: {
  currentPage: number;
  pageSize: number;
  status: "ALL" | "PENDING" | "FAILED" | "SUCCESSFUL";
}) => {
  const { data = {} } = useQuery({
    queryFn: async () => {
      const res = await service.getTransactions({
        page: currentPage,
        pageSize,
        status: status === "ALL" ? undefined : status,
      });
      return res?.data?.data;
    },
    queryKey: ["transactions", status, pageSize, currentPage],
  });

  return data;
};
