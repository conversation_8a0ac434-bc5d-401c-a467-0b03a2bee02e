"use client";
import { Container } from "@/ui-components/layout/container";
import { Heading, SegmentedControl } from "@radix-ui/themes";
import { TransactionTable } from "./_components/transaction-table";
import { TransactionDTO } from "@/api/data-contracts";
import { Pagination } from "@/ui-components/pagination";
import { useState } from "react";
import { useTranslations } from "next-intl";
import { useTransactionHistoryData } from "./use-transaction-history-data";
import { KycGate } from "../_components/kyc-gate";

type StatusType = "ALL" | "PENDING" | "FAILED" | "SUCCESSFUL";

function TransactionHistory() {
  const t = useTranslations("Portal.TransactionHistory");
  const tCommon = useTranslations("Common");

  const [currentPage, setCurrentPage] = useState(1);
  const [status, setStatus] = useState<StatusType>("ALL");

  const data = useTransactionHistoryData({
    currentPage,
    pageSize: 10,
    status,
  });

  const { records = [], totalPages = 1 } = data;

  const filterOptions: {
    label: string;
    value: TransactionDTO["status"] | "ALL";
  }[] = [
    {
      label: t("filters.all"),
      value: "ALL",
    },
    {
      label: t("filters.pending"),
      value: "PENDING",
    },
    {
      label: t("filters.rejected"),
      value: "FAILED",
    },
    {
      label: t("filters.verified"),
      value: "SUCCESSFUL",
    },
  ];

  return (
    <main className="grow py-6 md:py-10 bg-[#F2F2F2]">
      <Container>
        <div className="section-content flex flex-col gap-6">
          <Heading as="h2" size="7">
            {t("title")}
          </Heading>

          <div className="flex flex-col gap-4">
            <div>
              <SegmentedControl.Root
                variant="surface"
                value={status}
                onValueChange={(value: StatusType) => {
                  setStatus(value);
                }}
              >
                {filterOptions.map((option) => (
                  <SegmentedControl.Item
                    key={option.value}
                    value={option.value ?? "ALL"}
                  >
                    {option.label}
                  </SegmentedControl.Item>
                ))}
              </SegmentedControl.Root>
            </div>

            <div className="flex flex-col gap-4">
              <TransactionTable data={records} />
              {totalPages > 1 && (
                <Pagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  onPageChange={setCurrentPage}
                  className="justify-end"
                  nextText={tCommon("next")}
                  previousText={tCommon("previous")}
                />
              )}
            </div>
          </div>
        </div>
      </Container>
    </main>
  );
}

export default function TransactionHistoryPage() {
  return (
    <KycGate type="route">
      <TransactionHistory />
    </KycGate>
  );
}
